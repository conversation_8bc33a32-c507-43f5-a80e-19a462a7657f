DOCKER_TAG := local
IMAGE_NAME := nginx

install-jq-pipeline:
	@wget -nv -O jq https://github.com/stedolan/jq/releases/download/jq-1.5/jq-linux64
	@chmod +x ./jq
	@cp jq /usr/bin

install-aws-pipeline:
	@curl -s "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
	@unzip -qq awscliv2.zip
	@./aws/install

assume-deployer-role:
	echo "Assuming role arn:aws:iam::$(AWS_ACCOUNT_ID):role/$(ROLE)"
	@aws sts assume-role-with-web-identity --role-arn "arn:aws:iam::$(AWS_ACCOUNT_ID):role/$(ROLE)" --role-session-name "AssumedRoleSession" --web-identity-token "$(BITBUCKET_STEP_OIDC_TOKEN)" > role_credentials.json
	@echo "export AWS_ACCESS_KEY_ID=$$(jq -r '.Credentials.AccessKeyId' role_credentials.json)" > source.env
	@echo "export AWS_SECRET_ACCESS_KEY=$$(jq -r '.Credentials.SecretAccessKey' role_credentials.json)" >> source.env
	@echo "export AWS_SESSION_TOKEN=$$(jq -r '.Credentials.SessionToken' role_credentials.json)" >> source.env

role:
	echo "Assumed role"
	@aws sts get-caller-identity

build:
	docker build -t $(IMAGE_NAME):$(DOCKER_TAG) .

tag:
	docker tag $(IMAGE_NAME):$(DOCKER_TAG) $(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_REGION).amazonaws.com/$(IMAGE_NAME):$(DOCKER_TAG)

push:
	docker push $(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_REGION).amazonaws.com/$(IMAGE_NAME):$(DOCKER_TAG)
