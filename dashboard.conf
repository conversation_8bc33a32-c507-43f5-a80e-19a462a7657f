proxy_cache_path  /data/nginx/cache  levels=1:2    keys_zone=STATIC:10m 
inactive=24h  max_size=1g;
resolver ******* ******* ipv6=off;
server
{
    
    listen 80 default_server;
    listen  [::]:80 default_server;
    listen 443 ssl http2 default_server;
    listen [::]:443 ssl http2 default_server;
    #these certificats are copied through helm parser
    ssl_certificate /etc/ssl/self_signed_ssl/server/cert.pem;
    ssl_certificate_key /etc/ssl/self_signed_ssl/server/key.pem;

    #cors headers
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
    add_header 'Access-Control-Allow-Headers' 'Authorization,Accept,Origin,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range';
    add_header 'Access-Control-Allow-Methods' 'GET,OPTIONS';

    include /etc/nginx/conf.d/dashboard-configs/*.conf;
}