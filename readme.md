# Eyecue-Nginx
> ⚠️ **Deprecation Notice:** Once the legacy dashboard is decommissioned, this repository will be deprecated as well (est. Mar 2026). If this functionality is required after this date, it should be managed under Helm.

This repository currently provides the legacy dashboard access to images on the server e.g. for the "Best Shot" widget via a direct HTTPS. This means those who access these images must be connected via the EYECUE VPN.

This now uses self-signed certificate to proxy the http traffic to https. Means, the dashboard will be able to access the car images through SSL without throwing any mixed content issues. 

SSL certificates are pulled though helm parser from eyecue-helm-values dynamo table.

## Local testing
To test the repo locally, please uncomment the certificate copying lines on docker file.
2 certificates in the repo are sample certificates for mcd to test the container locally. These certificates will pulled from helm values automatically on production environments.