image: python:3.13.4

pipelines:
  tags:
    "*":
      - step:
          oidc: true
          name: "Pushing Image to ECR"
          caches:
            - docker
          services:
            - docker
          script:
            - make install-jq-pipeline
            - make install-aws-pipeline
            - make assume-deployer-role
            - source source.env
            - make role
            - aws configure set default.region $AWS_REGION
            - aws ecr get-login-password | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
            - make build
            - make tag DOCKER_TAG=$BITBUCKET_TAG
            - make push DOCKER_TAG=$BITBUCKET_TAG AWS_REGION=$AWS_REGION
