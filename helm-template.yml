{{- if $.Values.containers.nginx }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-cars
  labels:
    app: "nginx-cars"
  namespace: nmp-{{ .Values.global.hostname }}
spec:
  selector:
    matchLabels:
      app: "nginx-cars"
  template:
    metadata:
      labels:
        app: "nginx-cars"
    spec:
      hostNetwork: true
      containers:
      - name: nginx-cars
        image: "{{ $.Values.global.docker_registry }}/nginx:{{ $.Values.containers.nginx.image.tag }}"

        volumeMounts:
          {{- if $.Values.credentials.self_signed_ssl_certificate }}
          - name: ssl-certificates
            mountPath: "/etc/ssl/self_signed_ssl/"
            readOnly: true
          {{- end }}

          - name: eyecue-images
            mountPath: /usr/share/images/
            readOnly: true

          - name: cache
            mountPath: /data/nginx/cache

      volumes:
        {{- if $.Values.credentials.self_signed_ssl_certificate }}
        - name: ssl-certificates
          secret:
            secretName: eyeq-self-sign-ssl-credentials
            items:
              - key: CA_CERT
                path: ca/ca.pem
              - key: SERVER_KEY
                path: server/key.pem
              - key: SERVER_CERT
                path: server/cert.pem
        {{- end }}
        - name: eyecue-images
          hostPath:
            path: /media/fingermark/storage/nginx

        - name: cache
          hostPath:
            path: /media/fingermark/storage/dashboard-cache

      imagePullSecrets:
      - name: {{ .Values.global.imagePullSecrets }}

      nodeSelector:
        kubernetes.io/hostname: {{ .Values.global.hostname }}

{{- end }}